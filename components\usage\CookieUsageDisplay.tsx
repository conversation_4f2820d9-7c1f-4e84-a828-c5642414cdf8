'use client'

import { useState, useEffect } from 'react'
import { getUsageStats, formatTimeRemaining } from '@/lib/cookieRateLimit'
import { Clock, Zap, AlertCircle, CheckCircle } from 'lucide-react'

interface UsageStats {
  used: number
  remaining: number
  total: number
  canRequest: boolean
  resetTime: number
  timeUntilReset: number
  message: string
}

interface CookieUsageDisplayProps {
  compact?: boolean
  className?: string
}

export default function CookieUsageDisplay({ compact = false, className = '' }: CookieUsageDisplayProps) {
  const [stats, setStats] = useState<UsageStats | null>(null)
  const [mounted, setMounted] = useState(false)

  const updateStats = () => {
    if (typeof window !== 'undefined') {
      const usageStats = getUsageStats()
      setStats(usageStats)
    }
  }

  useEffect(() => {
    setMounted(true)
    updateStats()
    
    // Update stats every 30 seconds
    const interval = setInterval(updateStats, 30000)
    return () => clearInterval(interval)
  }, [])

  // Don't render on server side to avoid hydration mismatch
  if (!mounted || !stats) {
    return null
  }

  if (compact) {
    return (
      <div className={`flex items-center space-x-2 text-sm ${className}`}>
        <div className="flex items-center space-x-1">
          <Zap size={14} className={stats.canRequest ? "text-green-500" : "text-red-500"} />
          <span className="text-gray-600">
            {stats.remaining}/{stats.total}
          </span>
        </div>
        {!stats.canRequest && stats.timeUntilReset > 0 && (
          <div className="flex items-center space-x-1 text-orange-600">
            <Clock size={14} />
            <span>{formatTimeRemaining(stats.timeUntilReset)}</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 shadow-sm ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">Daily Usage</h3>
        <div className="flex items-center space-x-1">
          {stats.canRequest ? (
            <CheckCircle size={16} className="text-green-500" />
          ) : (
            <AlertCircle size={16} className="text-red-500" />
          )}
        </div>
      </div>

      {/* Usage Bar */}
      <div className="mb-3">
        <div className="flex justify-between text-xs text-gray-600 mb-1">
          <span>Requests Used</span>
          <span>{stats.used}/{stats.total}</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              stats.canRequest
                ? stats.remaining <= 1
                  ? 'bg-orange-500'
                  : 'bg-green-500'
                : 'bg-red-500'
            }`}
            style={{
              width: `${(stats.used / stats.total) * 100}%`
            }}
          />
        </div>
      </div>

      {/* Status Message */}
      <p className="text-sm text-gray-600 mb-2">
        {stats.message}
      </p>

      {/* Reset Timer */}
      {!stats.canRequest && stats.timeUntilReset > 0 && (
        <div className="flex items-center space-x-2 text-xs text-orange-600 bg-orange-50 rounded-md p-2">
          <Clock size={14} />
          <span>
            Resets in {formatTimeRemaining(stats.timeUntilReset)}
          </span>
        </div>
      )}

      {/* Available Requests */}
      {stats.canRequest && (
        <div className="flex items-center space-x-2 text-xs text-green-600 bg-green-50 rounded-md p-2">
          <Zap size={14} />
          <span>
            {stats.remaining} request{stats.remaining !== 1 ? 's' : ''} remaining
          </span>
        </div>
      )}
    </div>
  )
}

// Compact usage indicator for header with hover tooltip
export function CookieUsageIndicator({ className = '' }: { className?: string }) {
  const [stats, setStats] = useState<UsageStats | null>(null)
  const [mounted, setMounted] = useState(false)

  const updateStats = () => {
    if (typeof window !== 'undefined') {
      const usageStats = getUsageStats()
      setStats(usageStats)
    }
  }

  useEffect(() => {
    setMounted(true)
    updateStats()

    // Update stats every 30 seconds
    const interval = setInterval(updateStats, 30000)
    return () => clearInterval(interval)
  }, [])

  // Don't render on server side
  if (!mounted || !stats) {
    return null
  }

  const getTooltipContent = () => {
    if (!stats.canRequest && stats.timeUntilReset > 0) {
      return `Daily limit reached (${stats.used}/${stats.total} requests used). Resets in ${formatTimeRemaining(stats.timeUntilReset)}`
    } else if (stats.used === 0) {
      return `You have ${stats.total} AI agent requests available today`
    } else {
      return `${stats.remaining} of ${stats.total} AI agent requests remaining today`
    }
  }

  return (
    <div className={`relative group ${className}`}>
      <div className="flex items-center space-x-2 px-3 py-1 bg-gray-50 rounded-full text-xs cursor-help">
        <Zap
          size={12}
          className={stats.canRequest ? "text-green-500" : "text-red-500"}
        />
        <span className="text-gray-600">
          {stats.remaining}/{stats.total}
        </span>
        {!stats.canRequest && stats.timeUntilReset > 0 && (
          <>
            <span className="text-gray-400">•</span>
            <Clock size={12} className="text-orange-500" />
            <span className="text-orange-600">
              {formatTimeRemaining(stats.timeUntilReset)}
            </span>
          </>
        )}
      </div>

      {/* Hover Tooltip - Fixed at bottom of screen */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 px-4 py-3 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-50 pointer-events-none shadow-lg">
        {getTooltipContent()}
      </div>
    </div>
  )
}

// Hook for getting usage stats in other components
export function useUsageStats() {
  const [stats, setStats] = useState<UsageStats | null>(null)
  const [mounted, setMounted] = useState(false)

  const updateStats = () => {
    if (typeof window !== 'undefined') {
      const usageStats = getUsageStats()
      setStats(usageStats)
    }
  }

  useEffect(() => {
    setMounted(true)
    updateStats()
    
    const interval = setInterval(updateStats, 30000)
    return () => clearInterval(interval)
  }, [])

  return mounted ? stats : null
}
