"use client"

import type React from "react"
import { useState, useEffect } from "react"
import Image from "next/image"
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Bell, User, Plus, Home, BarChart3, Settings, HelpCircle, Sparkles, Upload, Sun, Moon, Copy, Check, LogOut } from "lucide-react"
import { checkRateLimit, recordRequest, formatTimeRemaining, getUsageStats } from "@/lib/cookieRateLimit"
import { CookieUsageIndicator } from "@/components/usage/CookieUsageDisplay"
import { SignInButton, SignUpButton, SignedIn, SignedOut, UserButton, useUser } from "@clerk/nextjs"

interface ChatMessage {
  id: string
  text: string
  sender: 'user' | 'agent'
  timestamp: Date
  code?: string
}

export default function Dashboard() {
  const { user, isLoaded } = useUser()
  const [codePromise, setCodePromise] = useState<Promise<string> | null>(null)
  const [isCodeActive, setIsCodeActive] = useState(false)
  const [hasStartedChat, setHasStartedChat] = useState(false)
  const [inputValue, setInputValue] = useState("")
  const [isDarkMode, setIsDarkMode] = useState(false) // Dark mode disabled - always light mode
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [conversationId, setConversationId] = useState<string>('default')
  const [currentCode, setCurrentCode] = useState<string>("")
  const [isCopied, setIsCopied] = useState(false)

  // Handle code promise resolution
  useEffect(() => {
    if (codePromise) {
      codePromise.then((code) => {
        setCurrentCode(code)
      }).catch((error) => {
        console.error('Error loading code:', error)
        setCurrentCode('// Error loading code')
      })
    }
  }, [codePromise])

  const copyToClipboard = async () => {
    if (currentCode) {
      try {
        await navigator.clipboard.writeText(currentCode)
        setIsCopied(true)
        setTimeout(() => setIsCopied(false), 2000)
      } catch (err) {
        console.error('Failed to copy code:', err)
      }
    }
  }

  const detectLanguage = (code: string): string => {
    if (code.includes('def ') || code.includes('import ') || code.includes('print(')) return 'python'
    if (code.includes('function ') || code.includes('const ') || code.includes('let ')) return 'javascript'
    if (code.includes('public class') || code.includes('import java')) return 'java'
    if (code.includes('#include') || code.includes('int main')) return 'cpp'
    if (code.includes('func ') || code.includes('package main')) return 'go'
    return 'python' // default to python since your server likely generates Python code
  }

  const sendMessageToAgent = async (message: string) => {
    if (!message.trim()) return

    // Check rate limit before making request
    const rateLimitCheck = checkRateLimit()
    if (!rateLimitCheck.allowed) {
      // Add rate limit message to chat
      const rateLimitMessage: ChatMessage = {
        id: Date.now().toString(),
        text: `⚠️ ${rateLimitCheck.message}`,
        sender: 'agent',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, rateLimitMessage])
      return
    }

    setIsLoading(true)

    // Add user message to chat
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: message,
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue("")

    try {
      console.log('Sending message to API:', message)

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          conversation_id: conversationId,
        }),
      })

      console.log('API response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API response error:', errorText)
        throw new Error(`API request failed with status ${response.status}: ${errorText}`)
      }

      const data = await response.json()
      console.log('API response data:', data)

      if (data.success) {
        // Add agent response to chat
        const agentMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          text: data.response,
          sender: 'agent',
          timestamp: new Date(),
          code: data.code
        }

        setMessages(prev => [...prev, agentMessage])

        // Record the successful request for rate limiting
        const recordResult = recordRequest()
        console.log('Request recorded:', recordResult)

        // If there's code in the response, show it in the code output section
        if (data.code) {
          const codePromise = Promise.resolve(data.code)
          setCodePromise(codePromise)
          setIsCodeActive(true)
        }

        // Update conversation ID if provided
        if (data.conversation_id) {
          setConversationId(data.conversation_id)
        }
      } else {
        throw new Error(data.error || 'Failed to get response from agent')
      }
    } catch (error) {
      console.error('Error sending message:', error)

      // Add error message to chat
      const errorMessage: ChatMessage = {
        id: (Date.now() + 2).toString(),
        text: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        sender: 'agent',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleGenerate = () => {
    console.log("[v0] Generate button clicked")
    setHasStartedChat(true)

    if (inputValue.trim()) {
      sendMessageToAgent(inputValue)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleGenerate()
    }
  }

  const handleExampleClick = (prompt: string) => {
    setInputValue(prompt)
  }

  const toggleTheme = () => {
    // Dark mode functionality disabled - coming soon
    console.log('Dark mode coming soon!')
  }

  return (
    <div className={`h-screen max-h-screen flex overflow-hidden ${isDarkMode ? "bg-gray-900" : "bg-gray-50"}`}>
      {/* Left Sidebar */}
      <div
        className={`w-16 border-r flex flex-col items-center py-6 space-y-6 ${
          isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"
        }`}
      >
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: "#1B0033" }}>
          <Image
            src="/logo.svg.png"
            alt="Logo"
            width={16}
            height={16}
            className="w-4 h-4"
          />
        </div>

        <nav className="flex flex-col space-y-4">
          <button
            className="w-10 h-10 flex items-center justify-center rounded-lg text-white hover:opacity-80"
            style={{ backgroundColor: "#007BFF" }}
          >
            <Home size={20} />
          </button>
          <div className="relative group">
            <button
              className={`w-10 h-10 flex items-center justify-center rounded-lg cursor-not-allowed ${
                isDarkMode ? "text-gray-500" : "text-gray-300"
              }`}
              disabled
            >
              <BarChart3 size={20} />
            </button>
            <div className="absolute left-12 top-1/2 -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Coming Soon
            </div>
          </div>

          <div className="relative group">
            <button
              className={`w-10 h-10 flex items-center justify-center rounded-lg cursor-not-allowed ${
                isDarkMode ? "text-gray-500" : "text-gray-300"
              }`}
              disabled
            >
              <Settings size={20} />
            </button>
            <div className="absolute left-12 top-1/2 -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Coming Soon
            </div>
          </div>
        </nav>

        <div className={`border-t pt-4 ${isDarkMode ? "border-gray-700" : "border-gray-200"}`}>
          <div className="relative group">
            <button
              className={`w-10 h-10 flex items-center justify-center rounded-lg cursor-not-allowed ${
                isDarkMode ? "text-gray-500" : "text-gray-300"
              }`}
              disabled
            >
              <Upload size={20} />
            </button>
            <div className="absolute left-12 top-1/2 -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Import Data - Coming Soon
            </div>
          </div>
        </div>

        <div className="flex-1"></div>

        <div className="relative group">
          <button
            className={`w-10 h-10 flex items-center justify-center rounded-lg cursor-not-allowed ${
              isDarkMode ? "text-gray-500" : "text-gray-300"
            }`}
            disabled
          >
            <HelpCircle size={20} />
          </button>
          <div className="absolute left-12 top-1/2 -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Coming Soon
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className={`flex-1 flex transition-all duration-500 ease-in-out h-full max-h-screen overflow-hidden ${isCodeActive ? "mr-0" : ""}`}>
        {/* Chat Panel */}
        <div className={`flex flex-col transition-all duration-500 ease-in-out h-full max-h-screen overflow-hidden ${isCodeActive ? "w-1/2" : "w-full"}`}>
          {/* Top Header */}
          <header
            className={`h-16 border-b flex items-center justify-between px-6 ${
              isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"
            }`}
          >
            <div className="flex items-center space-x-3">
              <Image
                src="/logo.svg.png"
                alt="Logo"
                width={32}
                height={32}
                className="w-8 h-8"
              />
              <h1 className={`text-lg font-medium ${isDarkMode ? "text-white" : "text-gray-900"}`}>Nexa</h1>
            </div>

            <div className="flex items-center space-x-4">
              <div className="relative group">
                <button
                  className="w-8 h-8 flex items-center justify-center rounded-full transition-colors text-gray-400 cursor-not-allowed"
                  disabled
                >
                  <Moon size={18} />
                </button>
                <div className="absolute right-0 top-10 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                  Coming Soon
                </div>
              </div>

              {/* Usage Indicator */}
              <CookieUsageIndicator />

              <div className="relative"></div>
              <Button size="sm" className="text-white hover:opacity-80" style={{ backgroundColor: "#1B0033" }}>
                <Plus size={16} className="mr-2" />
                New Project
              </Button>
              <button
                className={`w-8 h-8 flex items-center justify-center rounded-full transition-colors ${
                  isDarkMode ? "text-gray-300 hover:bg-gray-700" : "text-gray-400 hover:bg-gray-100"
                }`}
              >
                <Bell size={18} />
              </button>

              {/* Clerk Authentication */}
              <SignedOut>
                <div className="flex items-center space-x-3">
                  <SignInButton mode="modal">
                    <Button
                      size="sm"
                      className="text-white hover:opacity-90 font-medium px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
                      style={{ background: "linear-gradient(135deg, #007BFF, #4B0082)" }}
                    >
                      Sign In
                    </Button>
                  </SignInButton>
                  <SignUpButton mode="modal">
                    <Button
                      size="sm"
                      variant="outline"
                      className="font-medium px-4 py-2 rounded-lg border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white transition-all duration-200"
                    >
                      Sign Up
                    </Button>
                  </SignUpButton>
                </div>
              </SignedOut>

              <SignedIn>
                <UserButton
                  appearance={{
                    elements: {
                      avatarBox: "w-8 h-8"
                    }
                  }}
                />
              </SignedIn>
            </div>
          </header>

          {!hasStartedChat ? (
            /* Welcome Screen */
            <main className="flex-1 flex flex-col items-center justify-center px-6 py-12 overflow-y-auto">
              <div className="w-full max-w-2xl text-center space-y-8">
                {/* Avatar */}
                <div className="flex justify-center">
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center"
                    style={{ background: "linear-gradient(135deg, #4B0082, #8A2BE2)" }}
                  >
                    <Image
                      src="/logo.svg.png"
                      alt="Logo"
                      width={32}
                      height={32}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  </div>
                </div>

                {/* Greeting */}
                <div className="space-y-2">
                  <h2 className={`text-2xl font-semibold ${isDarkMode ? "text-white" : "text-gray-900"}`}>
                    <SignedIn>
                      Good Afternoon, {user?.firstName || 'there'}
                    </SignedIn>
                    <SignedOut>
                      Welcome to Nexa
                    </SignedOut>
                  </h2>
                  <p className={`${isDarkMode ? "text-gray-300" : "text-gray-600"}`}>
                    <SignedIn>
                      Ready to build your <span style={{ color: "#4B0082" }}>AI Agent</span>?
                    </SignedIn>
                    <SignedOut>
                      Sign in to start building your <span style={{ color: "#4B0082" }}>AI Agent</span>
                    </SignedOut>
                  </p>
                </div>

                {/* Authentication Buttons for Unauthenticated Users */}
                <SignedOut>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <SignInButton mode="modal">
                      <Button
                        className="w-full sm:w-auto px-8 py-3 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                        style={{ background: "linear-gradient(135deg, #007BFF, #4B0082)" }}
                      >
                        Sign In
                      </Button>
                    </SignInButton>
                    <SignUpButton mode="modal">
                      <Button
                        variant="outline"
                        className="w-full sm:w-auto px-8 py-3 font-medium rounded-xl border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white transition-all duration-200"
                      >
                        Create Account
                      </Button>
                    </SignUpButton>
                  </div>
                </SignedOut>

                {/* Input Box */}
                <div className="relative">
                  <div
                    className={`border rounded-2xl shadow-sm p-4 ${
                      isDarkMode ? "bg-gray-800 border-gray-600" : "bg-white border-gray-200"
                    }`}
                  >
                    <SignedIn>
                      <textarea
                        placeholder="Describe the type of agent you want to build..."
                        className={`w-full border-none bg-transparent focus:ring-0 focus:outline-none p-0 resize-none min-h-[24px] max-h-[200px] overflow-y-auto mb-3 ${
                          isDarkMode ? "text-white placeholder-gray-400" : "text-gray-900 placeholder-gray-500"
                        }`}
                        rows={1}
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onInput={(e) => {
                          const target = e.target as HTMLTextAreaElement
                          target.style.height = "auto"
                          target.style.height = Math.min(target.scrollHeight, 200) + "px"
                        }}
                        onKeyDown={handleKeyDown}
                      />
                    </SignedIn>

                    <SignedOut>
                      <textarea
                        placeholder="Sign in to start building your AI agent..."
                        className={`w-full border-none bg-transparent focus:ring-0 focus:outline-none p-0 resize-none min-h-[24px] max-h-[200px] overflow-y-auto mb-3 cursor-not-allowed ${
                          isDarkMode ? "text-white placeholder-gray-400" : "text-gray-900 placeholder-gray-500"
                        }`}
                        rows={1}
                        disabled
                      />
                    </SignedOut>

                    <div className="flex justify-end">
                      <SignedIn>
                        <Button
                          className="text-white hover:opacity-90 rounded-xl px-6"
                          style={{ background: "linear-gradient(135deg, #007BFF, #4B0082)" }}
                          onClick={handleGenerate}
                        >
                          Generate
                        </Button>
                      </SignedIn>

                      <SignedOut>
                        <SignInButton mode="modal">
                          <Button
                            className="text-white hover:opacity-90 rounded-xl px-6"
                            style={{ background: "linear-gradient(135deg, #007BFF, #4B0082)" }}
                          >
                            Sign In to Generate
                          </Button>
                        </SignInButton>
                      </SignedOut>
                    </div>
                  </div>
                </div>

                {/* Example Prompts */}
                <div className="space-y-4">
                  <p
                    className={`text-sm font-medium uppercase tracking-wide ${
                      isDarkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  >
                    Get started with example prompts
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button
                      className={`text-left p-4 border rounded-xl hover:shadow-sm transition-all ${
                        isDarkMode ? "bg-gray-800 border-gray-600 hover:border-blue-500" : "bg-white border-gray-200"
                      }`}
                      style={{ "--hover-border-color": "#007BFF" } as React.CSSProperties}
                      onMouseEnter={(e) => (e.currentTarget.style.borderColor = "#007BFF")}
                      onMouseLeave={(e) => (e.currentTarget.style.borderColor = "")}
                      onClick={() => handleExampleClick("Build an AI agent that manages my daily schedule")}
                    >
                      <p className={`font-medium ${isDarkMode ? "text-white" : "text-gray-900"}`}>
                        Build an AI agent that manages my daily schedule
                      </p>
                    </button>

                    <button
                      className={`text-left p-4 border rounded-xl hover:shadow-sm transition-all ${
                        isDarkMode ? "bg-gray-800 border-gray-600 hover:border-blue-500" : "bg-white border-gray-200"
                      }`}
                      onMouseEnter={(e) => (e.currentTarget.style.borderColor = "#007BFF")}
                      onMouseLeave={(e) => (e.currentTarget.style.borderColor = "")}
                      onClick={() => handleExampleClick("Create an AI assistant ready to pitch to clients")}
                    >
                      <p className={`font-medium ${isDarkMode ? "text-white" : "text-gray-900"}`}>
                        Create an AI assistant ready to pitch to clients
                      </p>
                    </button>

                    <button
                      className={`text-left p-4 border rounded-xl hover:shadow-sm transition-all ${
                        isDarkMode ? "bg-gray-800 border-gray-600 hover:border-blue-500" : "bg-white border-gray-200"
                      }`}
                      onMouseEnter={(e) => (e.currentTarget.style.borderColor = "#007BFF")}
                      onMouseLeave={(e) => (e.currentTarget.style.borderColor = "")}
                      onClick={() => handleExampleClick("Summarize reports into actionable insights")}
                    >
                      <p className={`font-medium ${isDarkMode ? "text-white" : "text-gray-900"}`}>
                        {"Make a Ai agent for stocks analytics"}
                      </p>
                    </button>

                    <button
                      className={`text-left p-4 border rounded-xl hover:shadow-sm transition-all ${
                        isDarkMode ? "bg-gray-800 border-gray-600 hover:border-blue-500" : "bg-white border-gray-200"
                      }`}
                      onMouseEnter={(e) => (e.currentTarget.style.borderColor = "#007BFF")}
                      onMouseLeave={(e) => (e.currentTarget.style.borderColor = "")}
                      onClick={() => handleExampleClick("Deploy a sales & support agent in minutes")}
                    >
                      <p className={`font-medium ${isDarkMode ? "text-white" : "text-gray-900"}`}>
                        {"How to set up a agent "}
                      </p>
                    </button>
                  </div>
                </div>
              </div>
            </main>
          ) : (
            /* Chat Interface */
            <div className="flex-1 overflow-hidden">
              <div className={`flex flex-col h-full ${isDarkMode ? "bg-gray-900" : "bg-gray-50"}`}>
                {/* Messages Area */}
                <div className="flex-1 overflow-y-auto p-6">
                  <div className="max-w-3xl mx-auto space-y-4">
                    {messages.map((message) => (
                      <div key={message.id} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                        {message.sender === 'agent' && (
                          <div className="flex items-start space-x-3">
                            <div
                              className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
                              style={{ background: "linear-gradient(135deg, #4B0082, #8A2BE2)" }}
                            >
                              <Image
                                src="/logo.svg.png"
                                alt="Logo"
                                width={16}
                                height={16}
                                className="w-4 h-4"
                              />
                            </div>
                            <div
                              className={`border rounded-2xl px-4 py-3 max-w-md shadow-sm ${
                                isDarkMode ? "bg-gray-800 border-gray-600" : "bg-white border-gray-200"
                              }`}
                            >
                              <p className={isDarkMode ? "text-white" : "text-gray-900"}>
                                {message.text}
                              </p>
                              {message.code && (
                                <button
                                  onClick={() => {
                                    if (message.code) {
                                      const codePromise = Promise.resolve(message.code)
                                      setCodePromise(codePromise)
                                      setIsCodeActive(true)
                                    }
                                  }}
                                  className="mt-2 text-xs text-blue-500 hover:text-blue-600 transition-colors cursor-pointer underline decoration-dotted underline-offset-2 hover:decoration-solid"
                                  title="Click to open code output panel"
                                >
                                  ✨ Code generated - check the output panel →
                                </button>
                              )}
                            </div>
                          </div>
                        )}
                        {message.sender === 'user' && (
                          <div
                            className={`border rounded-2xl px-4 py-3 max-w-md shadow-sm ${
                              isDarkMode ? "bg-gray-800 border-gray-600" : "bg-white border-gray-200"
                            }`}
                          >
                            <p className={isDarkMode ? "text-white" : "text-gray-900"}>
                              {message.text}
                            </p>
                          </div>
                        )}
                      </div>
                    ))}

                    {isLoading && (
                      <div className="flex justify-start">
                        <div className="flex items-start space-x-3">
                          <div
                            className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
                            style={{ background: "linear-gradient(135deg, #4B0082, #8A2BE2)" }}
                          >
                            <Image
                              src="/logo.svg.png"
                              alt="Logo"
                              width={16}
                              height={16}
                              className="w-4 h-4 animate-pulse"
                            />
                          </div>
                          <div
                            className={`border rounded-2xl px-4 py-3 max-w-md shadow-sm ${
                              isDarkMode ? "bg-gray-800 border-gray-600" : "bg-white border-gray-200"
                            }`}
                          >
                            <p className={`${isDarkMode ? "text-white" : "text-gray-900"} animate-pulse`}>
                              Thinking...
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Input Area */}
                <div
                  className={`border-t p-4 ${isDarkMode ? "border-gray-700 bg-gray-800" : "border-gray-200 bg-white"}`}
                >
                  <div className="max-w-3xl mx-auto">
                    <div
                      className={`border rounded-2xl p-3 ${
                        isDarkMode ? "bg-gray-700 border-gray-600" : "bg-gray-50 border-gray-200"
                      }`}
                    >
                      <textarea
                        placeholder="Ask a follow-up question or request modifications..."
                        className={`w-full border-none bg-transparent focus:ring-0 focus:outline-none p-0 resize-none min-h-[24px] max-h-[120px] overflow-y-auto mb-2 ${
                          isDarkMode ? "text-white placeholder-gray-400" : "text-gray-900 placeholder-gray-500"
                        }`}
                        rows={1}
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter" && !e.shiftKey) {
                            e.preventDefault()
                            if (!isLoading && inputValue.trim()) {
                              sendMessageToAgent(inputValue)
                            }
                          }
                        }}
                        onInput={(e) => {
                          const target = e.target as HTMLTextAreaElement
                          target.style.height = "auto"
                          target.style.height = Math.min(target.scrollHeight, 120) + "px"
                        }}
                        disabled={isLoading}
                      />
                      <div className="flex justify-end">
                        <Button
                          size="sm"
                          className="text-white hover:opacity-90 rounded-xl px-4 disabled:opacity-50"
                          style={{ background: "linear-gradient(135deg, #007BFF, #4B0082)" }}
                          onClick={() => {
                            if (!isLoading && inputValue.trim()) {
                              sendMessageToAgent(inputValue)
                            }
                          }}
                          disabled={isLoading || !inputValue.trim()}
                        >
                          {isLoading ? "Sending..." : "Send"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Code Panel */}
        <div
          className={`transition-all duration-500 ease-in-out border-l h-full max-h-screen overflow-hidden ${
            isCodeActive ? "w-1/2 opacity-100" : "w-0 opacity-0 overflow-hidden"
          } ${isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"}`}
        >
          {isCodeActive && (
            <div className="h-full flex flex-col max-h-screen overflow-hidden">
              {/* Code Panel Header */}
              <div
                className={`h-16 border-b flex items-center justify-between px-4 ${
                  isDarkMode ? "border-gray-700" : "border-gray-200"
                }`}
              >
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span className={`ml-4 text-sm font-medium ${isDarkMode ? "text-gray-300" : "text-gray-700"}`}>
                    Generated Agent Output Code
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  {currentCode && (
                    <button
                      onClick={copyToClipboard}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                        isCopied
                          ? isDarkMode
                            ? "bg-green-600 hover:bg-green-700 text-white"
                            : "bg-green-500 hover:bg-green-600 text-white"
                          : isDarkMode
                          ? "bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl"
                          : "bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl"
                      }`}
                      title={isCopied ? "Code copied!" : "Copy code to clipboard"}
                    >
                      {isCopied ? (
                        <>
                          <Check size={16} />
                          <span>Copied!</span>
                        </>
                      ) : (
                        <>
                          <Copy size={16} />
                          <span>Copy Code</span>
                        </>
                      )}
                    </button>
                  )}
                  <button
                    onClick={() => setIsCodeActive(false)}
                    className={`p-2 rounded-lg transition-colors ${
                      isDarkMode
                        ? "text-gray-400 hover:text-gray-300 hover:bg-gray-700"
                        : "text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                    }`}
                    title="Close code panel"
                  >
                    ×
                  </button>
                </div>
              </div>

              {/* Code Content */}
              <div className="flex-1 overflow-hidden relative" style={{ minHeight: 0 }}>
                <div className="h-full max-h-full overflow-hidden">
                  {currentCode ? (
                    <div className="h-full overflow-hidden relative">
                      {/* Scrollable Code Container */}
                      <div
                        className="h-full max-h-full overflow-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent"
                        style={{
                          scrollbarWidth: 'thin',
                          scrollbarColor: isDarkMode ? '#4B5563 transparent' : '#9CA3AF transparent',
                          maxHeight: 'calc(100vh - 64px)' // Account for header height
                        }}
                      >
                        <SyntaxHighlighter
                          language={detectLanguage(currentCode)}
                          style={vscDarkPlus}
                          customStyle={{
                            margin: 0,
                            padding: '1.5rem',
                            background: isDarkMode ? '#0d1117' : '#1e1e1e',
                            fontSize: '14px',
                            lineHeight: '1.6',
                            borderRadius: '0',
                            minHeight: 'auto',
                            height: 'auto',
                            overflow: 'visible',
                            display: 'block'
                          }}
                          showLineNumbers={true}
                          wrapLines={false}
                          wrapLongLines={false}
                          PreTag="div"
                        >
                          {currentCode}
                        </SyntaxHighlighter>
                      </div>

                      {/* Floating Copy Button for Long Code */}
                      {currentCode && currentCode.split('\n').length > 20 && (
                        <button
                          onClick={copyToClipboard}
                          className={`absolute bottom-4 right-4 p-3 rounded-full shadow-lg transition-all duration-200 z-10 ${
                            isCopied
                              ? "bg-green-500 hover:bg-green-600 text-white"
                              : "bg-blue-500 hover:bg-blue-600 text-white hover:shadow-xl"
                          }`}
                          title={isCopied ? "Code copied!" : "Copy code to clipboard"}
                        >
                          {isCopied ? <Check size={20} /> : <Copy size={20} />}
                        </button>
                      )}
                    </div>
                  ) : (
                    <div
                      className={`h-full max-h-full flex items-center justify-center border-t overflow-hidden ${
                        isDarkMode ? "bg-gray-900 border-gray-700" : "bg-gray-800 border-gray-600"
                      }`}
                      style={{ maxHeight: 'calc(100vh - 64px)' }}
                    >
                      <div className="text-center p-8">
                        <div className={`text-lg font-medium mb-3 ${isDarkMode ? "text-gray-300" : "text-gray-400"}`}>
                          No code generated yet
                        </div>
                        <div className={`text-sm ${isDarkMode ? "text-gray-500" : "text-gray-500"}`}>
                          Send a message to the agent to generate code
                        </div>
                        <div className={`mt-4 text-xs ${isDarkMode ? "text-gray-600" : "text-gray-600"}`}>
                          Generated code will appear here with syntax highlighting
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
