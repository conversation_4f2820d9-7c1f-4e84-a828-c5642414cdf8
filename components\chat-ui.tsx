"use client"

import { useState } from "react"

export default function ChatUI() {
  const [messages, setMessages] = useState([])
  const [input, setInput] = useState("")

  const sendMessage = () => {
    if (!input.trim()) return
    setMessages([...messages, { text: input, sender: "user" }])
    setInput("")

    // simulate bot/agent reply
    setTimeout(() => {
      setMessages((m) => [...m, { text: "Got it ✅", sender: "bot" }])
    }, 800)
  }

  return (
    <div className="flex flex-col h-screen bg-gray-100">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4">
        {messages.map((m, i) => (
          <div key={i} className={`mb-2 flex ${m.sender === "user" ? "justify-end" : "justify-start"}`}>
            <div
              className={`px-3 py-2 rounded-2xl max-w-xs ${
                m.sender === "user" ? "bg-blue-600 text-white" : "bg-gray-300 text-black"
              }`}
            >
              {m.text}
            </div>
          </div>
        ))}
      </div>

      {/* Input box */}
      <div className="p-2 border-t flex">
        <input
          className="flex-1 border rounded-full px-3 py-2 mr-2"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && sendMessage()}
          placeholder="Type a message..."
        />
        <button className="bg-blue-600 text-white px-4 py-2 rounded-full" onClick={sendMessage}>
          Send
        </button>
      </div>
    </div>
  )
}
