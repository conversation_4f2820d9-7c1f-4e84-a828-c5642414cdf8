"use client"

import { useEffect, useState } from "react"
import Editor from "@monaco-editor/react"

interface CodeCanvasProps {
  codePromise: Promise<string> | null
  isInline?: boolean // Added isInline prop to support side panel mode
}

export default function CodeCanvas({ codePromise, isInline = false }: CodeCanvasProps) {
  const [open, setOpen] = useState(false)
  const [code, setCode] = useState("")

  useEffect(() => {
    if (!codePromise) return
    codePromise.then((fetchedCode) => {
      setCode(fetchedCode)
      setOpen(true)
    })
  }, [codePromise])

  if (!open && !isInline) return null

  if (isInline) {
    return (
      <div className="h-full bg-[#0b0b10] flex flex-col">
        <Editor
          height="100%"
          defaultLanguage="javascript"
          value={code}
          theme="vs-dark"
          options={{
            minimap: { enabled: false },
            fontSize: 13,
            lineNumbers: "on",
            scrollBeyondLastLine: false,
            automaticLayout: true,
            wordWrap: "on",
            readOnly: true,
          }}
        />
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-[#0b0b10] rounded-xl shadow-xl w-5/6 h-5/6 overflow-hidden">
        <div className="flex justify-between items-center p-4 border-b border-white/10">
          <h2 className="text-white font-semibold">Code Canvas</h2>
          <button
            onClick={() => setOpen(false)}
            className="text-white hover:text-gray-300 text-xl font-bold w-8 h-8 flex items-center justify-center"
          >
            ✕
          </button>
        </div>
        <Editor
          height="calc(100% - 60px)"
          defaultLanguage="javascript"
          value={code}
          theme="vs-dark"
          options={{
            minimap: { enabled: false },
            fontSize: 14,
            lineNumbers: "on",
            scrollBeyondLastLine: false,
            automaticLayout: true,
          }}
        />
      </div>
    </div>
  )
}
