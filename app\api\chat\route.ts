import { NextRequest, NextResponse } from 'next/server'

const PYTHON_SERVER_URL = 'https://asgard-z35h.onrender.com'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, conversation_id } = body

    console.log('Sending request to Python server:', { message })

    // Forward the request to your Python server using the correct /build endpoint
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

    const response = await fetch(`${PYTHON_SERVER_URL}/build`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        query: message,
        execute: false, // Set to true if you want to execute the code
      }),
      signal: controller.signal,
    })

    clearTimeout(timeoutId)

    console.log('Python server response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Python server error response:', errorText)
      throw new Error(`Python server responded with status: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    console.log('Python server response data:', data)

    // Extract the response text and code from the Python server response
    const responseText = data.stdout || 'Agent processed your request successfully.'
    const generatedCode = data.source || null

    return NextResponse.json({
      success: true,
      response: responseText,
      code: generatedCode,
      conversation_id: conversation_id || 'default',
    })
  } catch (error) {
    console.error('Error communicating with Python server:', error)

    let errorMessage = 'Failed to communicate with the agent server'
    let errorDetails = 'Unknown error'

    if (error instanceof Error) {
      errorDetails = error.message
      if (error.name === 'AbortError') {
        errorMessage = 'Request timed out'
        errorDetails = 'The server took too long to respond'
      } else if (error.message.includes('fetch')) {
        errorMessage = 'Network error'
        errorDetails = 'Could not connect to the agent server'
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: errorDetails,
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    status: 'Chat API is running',
    server_url: PYTHON_SERVER_URL,
  })
}
