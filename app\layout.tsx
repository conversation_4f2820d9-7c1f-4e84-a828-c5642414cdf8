import type { <PERSON>ada<PERSON> } from 'next'
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import { ClerkProvider } from '@clerk/nextjs'
import './globals.css'

export const metadata: Metadata = {
  title: 'Nexa - AI Agent Builder',
  description: 'Build powerful AI agents with Nexa - The intelligent agent creation platform',
  generator: 'Nexa',
  icons: {
    icon: '/logo.svg.png',
    shortcut: '/logo.svg.png',
    apple: '/logo.svg.png',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable}`}>
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
