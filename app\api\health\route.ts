import { NextResponse } from 'next/server'

const PYTHON_SERVER_URL = 'https://asgard-z35h.onrender.com'

export async function GET() {
  try {
    // Test connection to Python server by checking the docs endpoint
    const response = await fetch(`${PYTHON_SERVER_URL}/docs`, {
      method: 'GET',
      headers: {
        'Accept': 'text/html',
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    })

    if (response.ok) {
      return NextResponse.json({
        status: 'healthy',
        python_server: 'connected',
        python_server_url: PYTHON_SERVER_URL,
        available_endpoints: ['/build', '/docs', '/openapi.json'],
      })
    } else {
      return NextResponse.json({
        status: 'warning',
        python_server: 'unreachable',
        python_server_url: PYTHON_SERVER_URL,
        python_server_status: response.status,
      })
    }
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      python_server: 'error',
      python_server_url: PYTHON_SERVER_URL,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
