# 🤖 Nexa AI Agent Builder

A modern, intuitive web application for building and interacting with AI agents. Built with Next.js, TypeScript, and Tailwind CSS, featuring real-time code generation, syntax highlighting, and intelligent rate limiting.

![AI Agent Builder](https://img.shields.io/badge/Next.js-15.2.4-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.0-38B2AC?style=for-the-badge&logo=tailwind-css)

## ✨ Features

### 🎯 Core Functionality
- **AI Agent Interaction**: Natural language conversations with AI agents
- **Real-time Code Generation**: Generate code in multiple programming languages
- **Syntax Highlighting**: Beautiful code display with VS Code-like styling
- **Copy to Clipboard**: One-click code copying with visual feedback
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile

### 🔒 Authentication & Security
- **Firebase Authentication**: Secure email/password authentication
- **Demo Mode**: Full functionality without authentication setup
- **Protected Routes**: Secure access to AI agent features
- **User Management**: Profile display and session management

### ⚡ Rate Limiting
- **Cookie-Based Limiting**: 3 requests per user per 24-hour period
- **Rolling Window**: 24-hour timer from first request
- **Visual Feedback**: Real-time usage indicator in header
- **Smart Tooltips**: Detailed information on hover
- **Automatic Reset**: Clean slate after cooldown period

### 🎨 User Interface
- **Modern Design**: Clean, professional interface
- **Dark Mode Ready**: Prepared for dark theme (coming soon)
- **Smooth Animations**: Polished transitions and interactions
- **Code Panel**: Dedicated area for generated code with scrolling
- **Usage Indicator**: Compact display of remaining requests

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm, yarn, or pnpm
- (Optional) Firebase project for authentication

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-agent-builder
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   pnpm install
   # or
   yarn install
   ```

3. **Start development server**
   ```bash
   npm run dev
   # or
   pnpm dev
   # or
   yarn dev
   ```

4. **Open in browser**
   ```
   http://localhost:3000
   ```

The application will run in **Demo Mode** with full functionality and cookie-based rate limiting.

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the root directory:

```env
# Firebase Configuration (Optional - for authentication)
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

### Firebase Setup (Optional)

For full authentication features, follow the detailed setup guide in `FIREBASE_SETUP.md`:

1. Create Firebase project
2. Enable Email/Password authentication
3. Configure environment variables
4. Restart development server

## 📁 Project Structure

```
ai-agent-builder/
├── app/                          # Next.js app directory
│   ├── api/                      # API routes
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Main application
├── components/                   # React components
│   ├── auth/                     # Authentication components
│   │   ├── AuthModal.tsx         # Login/signup modal
│   │   ├── LoginForm.tsx         # Login form
│   │   └── SignupForm.tsx        # Registration form
│   ├── usage/                    # Usage tracking components
│   │   └── CookieUsageDisplay.tsx # Rate limit indicator
│   └── ui/                       # Reusable UI components
├── contexts/                     # React contexts
│   └── AuthContext.tsx           # Authentication context
├── lib/                          # Utility libraries
│   ├── cookieRateLimit.ts        # Cookie-based rate limiting
│   └── firebase.ts               # Firebase configuration
├── public/                       # Static assets
├── FIREBASE_SETUP.md             # Firebase setup guide
└── README.md                     # This file
```

## 🎮 Usage

### Basic Interaction

1. **Start a Conversation**
   - Type your request in the input field
   - Click "Generate" or press Enter
   - View the AI response in the chat

2. **Code Generation**
   - Request code in your message
   - Generated code appears in the right panel
   - Click "Copy Code" to copy to clipboard

3. **Rate Limiting**
   - Monitor usage with the header indicator
   - Hover for detailed information
   - Wait for reset after 3 requests

### Example Prompts

- "Build an AI agent that manages my daily schedule"
- "Create a Python script for data analysis"
- "Generate a React component for user authentication"
- "Write a Node.js API for handling file uploads"

## 🔄 Rate Limiting System

### How It Works

- **Limit**: 3 AI agent requests per user
- **Window**: 24-hour rolling period
- **Storage**: Browser cookies (persistent)
- **Reset**: Automatic after 24 hours

### Visual Indicators

- **Header Badge**: Shows `2/3` remaining requests
- **Color Coding**: Green (available) / Red (blocked)
- **Hover Tooltip**: Detailed status at bottom of screen
- **Chat Messages**: Rate limit notifications in conversation

## 🛠️ Development

### Available Scripts

```bash
# Development server
npm run dev

# Production build
npm run build

# Start production server
npm start

# Type checking
npm run type-check

# Linting
npm run lint
```

### Tech Stack

- **Framework**: Next.js 15.2.4
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Authentication**: Firebase Auth
- **State Management**: React Context
- **Code Highlighting**: react-syntax-highlighter
- **Icons**: Lucide React
- **UI Components**: Custom + shadcn/ui

## 🔒 Security Features

- **Environment Variables**: Sensitive data protected
- **Client-Side Rate Limiting**: Prevents abuse
- **Firebase Security Rules**: Database protection
- **Input Validation**: Secure form handling
- **HTTPS Ready**: Production security

## 🌐 Deployment

### Vercel (Recommended)

1. Push code to GitHub
2. Connect repository to Vercel
3. Add environment variables
4. Deploy automatically

### Other Platforms

- **Netlify**: Static site deployment
- **Railway**: Full-stack deployment
- **Docker**: Containerized deployment

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check `FIREBASE_SETUP.md` for setup help
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Use GitHub Discussions for questions

## 🎯 Roadmap

- [ ] Dark mode implementation
- [ ] Multiple AI model support
- [ ] Code export functionality
- [ ] Project templates
- [ ] Collaboration features
- [ ] Advanced rate limiting options

## 🐛 Troubleshooting

### Common Issues

**Firebase Authentication Not Working**
- Verify environment variables are set correctly
- Check Firebase console for enabled authentication methods
- Ensure domain is authorized in Firebase settings

**Rate Limiting Not Persisting**
- Check if cookies are enabled in browser
- Verify browser isn't in incognito/private mode
- Clear cookies and try again if issues persist

**Code Panel Not Showing**
- Ensure JavaScript is enabled
- Check browser console for errors
- Try refreshing the page

**API Requests Failing**
- Check network connection
- Verify API endpoints are accessible
- Review browser console for error messages

### Performance Tips

- **Clear Browser Cache**: Regularly clear cache for optimal performance
- **Update Browser**: Use latest browser version for best experience
- **Stable Connection**: Ensure stable internet for real-time features
- **Close Unused Tabs**: Free up memory for better performance

## 📊 Analytics & Monitoring

### Built-in Tracking

- **Usage Statistics**: Cookie-based request tracking
- **Error Logging**: Console-based error reporting
- **Performance Metrics**: Client-side performance monitoring

### Optional Integrations

- **Google Analytics**: Add GA4 for detailed analytics
- **Sentry**: Error tracking and performance monitoring
- **Vercel Analytics**: Built-in analytics for Vercel deployments

## 🔧 Customization

### Theming

The application uses Tailwind CSS for styling. Customize colors in `tailwind.config.js`:

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: '#4B0082',    // Purple brand color
        secondary: '#007BFF',  // Blue accent color
        // Add your custom colors
      }
    }
  }
}
```

### Rate Limiting

Modify rate limiting settings in `lib/cookieRateLimit.ts`:

```typescript
const MAX_REQUESTS = 3        // Change request limit
const COOLDOWN_HOURS = 24     // Change cooldown period
```

### UI Components

- **Header**: Modify in `app/page.tsx`
- **Chat Interface**: Customize chat styling
- **Code Panel**: Adjust syntax highlighting themes

## 🌍 Internationalization

The application is ready for internationalization:

- **Text Strings**: Centralized in components
- **Date/Time**: Uses browser locale
- **Number Formatting**: Locale-aware formatting
- **RTL Support**: CSS ready for right-to-left languages

## 📱 Mobile Optimization

- **Responsive Design**: Works on all screen sizes
- **Touch Friendly**: Optimized for touch interactions
- **Fast Loading**: Optimized for mobile networks
- **PWA Ready**: Can be installed as Progressive Web App

## 🔐 Privacy & Data

### Data Collection

- **Minimal Data**: Only essential data collected
- **Local Storage**: Usage data stored in browser cookies
- **No Tracking**: No third-party tracking by default
- **User Control**: Users can clear data anytime

### GDPR Compliance

- **Cookie Consent**: Implement cookie consent banner if needed
- **Data Export**: Users can export their data
- **Data Deletion**: Users can delete their data
- **Privacy Policy**: Add privacy policy for production use

## 🎓 Learning Resources

### Documentation

- **Next.js**: [nextjs.org/docs](https://nextjs.org/docs)
- **TypeScript**: [typescriptlang.org](https://www.typescriptlang.org/)
- **Tailwind CSS**: [tailwindcss.com](https://tailwindcss.com/)
- **Firebase**: [firebase.google.com/docs](https://firebase.google.com/docs)

### Tutorials

- **React Hooks**: Understanding useState and useEffect
- **Next.js Routing**: App directory and routing
- **Firebase Auth**: Authentication implementation
- **Tailwind Styling**: Responsive design patterns

---

**Built with ❤️ using Next.js and modern web technologies**

*Last updated: January 2025*
