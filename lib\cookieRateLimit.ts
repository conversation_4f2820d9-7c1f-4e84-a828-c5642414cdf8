// Simple cookie-based rate limiting
// Allows 3 messages per user with 24-hour cooldown

const COOKIE_NAME = 'ai_agent_usage'
const MAX_REQUESTS = 3
const COOLDOWN_HOURS = 24
const COOLDOWN_MS = COOLDOWN_HOURS * 60 * 60 * 1000 // 24 hours in milliseconds

export interface UsageData {
  requests: number
  firstRequestTime: number
  lastRequestTime: number
  resetTime: number
}

export interface RateLimitResult {
  allowed: boolean
  remaining: number
  total: number
  resetTime: number
  timeUntilReset: number
  message: string
}

/**
 * Get cookie value by name
 */
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null
  
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) {
    const cookieValue = parts.pop()?.split(';').shift()
    return cookieValue || null
  }
  return null
}

/**
 * Set cookie with expiration
 */
function setCookie(name: string, value: string, expirationMs: number): void {
  if (typeof document === 'undefined') return
  
  const expires = new Date(Date.now() + expirationMs).toUTCString()
  document.cookie = `${name}=${value}; expires=${expires}; path=/; SameSite=Strict`
}

/**
 * Delete cookie
 */
function deleteCookie(name: string): void {
  if (typeof document === 'undefined') return
  
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
}

/**
 * Get current usage data from cookie
 */
function getUsageData(): UsageData | null {
  const cookieValue = getCookie(COOKIE_NAME)
  if (!cookieValue) return null
  
  try {
    const data = JSON.parse(decodeURIComponent(cookieValue))
    return {
      requests: data.requests || 0,
      firstRequestTime: data.firstRequestTime || 0,
      lastRequestTime: data.lastRequestTime || 0,
      resetTime: data.resetTime || 0
    }
  } catch (error) {
    console.error('Error parsing usage cookie:', error)
    return null
  }
}

/**
 * Save usage data to cookie
 */
function saveUsageData(data: UsageData): void {
  const cookieValue = encodeURIComponent(JSON.stringify(data))
  // Set cookie to expire slightly after the reset time to ensure cleanup
  const expirationMs = data.resetTime - Date.now() + (60 * 60 * 1000) // +1 hour buffer
  setCookie(COOKIE_NAME, cookieValue, Math.max(expirationMs, COOLDOWN_MS))
}

/**
 * Check if user can make a request and get current status
 */
export function checkRateLimit(): RateLimitResult {
  const now = Date.now()
  let usageData = getUsageData()
  
  // If no usage data exists, user can make first request
  if (!usageData) {
    return {
      allowed: true,
      remaining: MAX_REQUESTS - 1, // Will be 2 after first request
      total: 0,
      resetTime: now + COOLDOWN_MS,
      timeUntilReset: COOLDOWN_MS,
      message: `You have ${MAX_REQUESTS} requests available`
    }
  }
  
  // Check if cooldown period has expired
  if (now >= usageData.resetTime) {
    // Reset the usage data
    deleteCookie(COOKIE_NAME)
    return {
      allowed: true,
      remaining: MAX_REQUESTS - 1,
      total: 0,
      resetTime: now + COOLDOWN_MS,
      timeUntilReset: COOLDOWN_MS,
      message: `Cooldown expired! You have ${MAX_REQUESTS} requests available`
    }
  }
  
  // Check if user has exceeded the limit
  if (usageData.requests >= MAX_REQUESTS) {
    const timeUntilReset = usageData.resetTime - now
    return {
      allowed: false,
      remaining: 0,
      total: usageData.requests,
      resetTime: usageData.resetTime,
      timeUntilReset,
      message: `Rate limit exceeded. ${formatTimeRemaining(timeUntilReset)} until reset`
    }
  }
  
  // User can make another request
  const remaining = MAX_REQUESTS - usageData.requests
  const timeUntilReset = usageData.resetTime - now
  
  return {
    allowed: true,
    remaining: remaining - 1, // Will be this after the request
    total: usageData.requests,
    resetTime: usageData.resetTime,
    timeUntilReset,
    message: `${remaining} requests remaining`
  }
}

/**
 * Record a new request
 */
export function recordRequest(): RateLimitResult {
  const now = Date.now()
  let usageData = getUsageData()
  
  if (!usageData || now >= usageData.resetTime) {
    // First request or after reset
    usageData = {
      requests: 1,
      firstRequestTime: now,
      lastRequestTime: now,
      resetTime: now + COOLDOWN_MS
    }
  } else {
    // Increment request count
    usageData = {
      ...usageData,
      requests: usageData.requests + 1,
      lastRequestTime: now
    }
  }
  
  saveUsageData(usageData)
  
  const remaining = Math.max(0, MAX_REQUESTS - usageData.requests)
  const timeUntilReset = usageData.resetTime - now
  
  return {
    allowed: usageData.requests <= MAX_REQUESTS,
    remaining,
    total: usageData.requests,
    resetTime: usageData.resetTime,
    timeUntilReset,
    message: remaining > 0 
      ? `${remaining} requests remaining` 
      : `Rate limit reached. ${formatTimeRemaining(timeUntilReset)} until reset`
  }
}

/**
 * Format time remaining in a human-readable format
 */
export function formatTimeRemaining(ms: number): string {
  if (ms <= 0) return '0 minutes'
  
  const hours = Math.floor(ms / (1000 * 60 * 60))
  const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else {
    return `${minutes}m`
  }
}

/**
 * Get usage statistics for display
 */
export function getUsageStats() {
  const result = checkRateLimit()
  return {
    used: result.total,
    remaining: result.allowed ? result.remaining + 1 : result.remaining, // +1 because we haven't made the request yet
    total: MAX_REQUESTS,
    canRequest: result.allowed,
    resetTime: result.resetTime,
    timeUntilReset: result.timeUntilReset,
    message: result.message
  }
}
